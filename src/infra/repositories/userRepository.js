const BaseRepository = require('./baseRepository');

class UserRepository extends BaseRepository {
  constructor({ db, constants }) {
    super(db.userModel);
    this.userConstants = constants.user;
    this.Sequelize = db.Sequelize;
    this.getUserOption = {
      include: [
        {
          association: 'role',
          attributes: ['id', 'name'],
          include: [
            {
              association: 'permissions',
              attributes: ['id', 'module', 'action'],
              through: { attributes: [] },
            },
          ],
        },
        {
          association: 'department',
          attributes: ['id', 'name'],
        },
        {
          association: 'supervisor',
          attributes: ['id', 'firstName', 'lastName'],
        },
      ],
      attributes: {
        exclude: ['roleId', 'departmentId', 'supervisorId'],
      },
    };
  }

  async createUser(payload, options = {}) {
    const userJSON = await this.create(payload, {
      transaction: options.transaction,
    });

    delete userJSON.password;
    delete userJSON.otpSecret;

    return userJSON;
  }

  async getUserById(userId, options = {}) {
    const { paranoid = false } = options;
    const user = await this.getById(userId, {
      ...this.getUserOption,
      paranoid,
    });

    return user;
  }

  async getUser(where = {}) {
    const user = await this.findOne({
      ...this.getUserOption,
      where,
      paranoid: false,
    });

    return user;
  }

  async updateUserById(userId, payload, options = {}) {
    const result = await this.update({ id: userId }, payload, {
      paranoid: false,
      ...options,
    });

    return result;
  }

  async getAllUsers(payload) {
    const { order, page, limit, paginate, whereClause } = payload;

    const userList = await this.findAll({
      order,
      paginate,
      page: page,
      limit: limit,
      paranoid: false,
      where: whereClause,
      include: [
        {
          association: 'department',
          attributes: ['id', 'name'],
        },
        {
          association: 'supervisor',
          attributes: ['id', 'firstName', 'lastName'],
        },
        {
          association: 'role',
          as: 'role',
          attributes: ['id', 'name'],
        },
      ],
      attributes: {
        exclude: [
          'password',
          'otpSecret',
          'roleId',
          'isPasswordTemporary',
          'departmentId',
          'supervisorId',
        ],
      },
    });

    return userList;
  }

  async getUsersByRoleName(payload) {
    const {
      roleNames,
      where = {},
      paranoid = false,
      attributes = ['id', 'firstName', 'lastName'],
    } = payload;
    const roleNamesArray = Array.isArray(roleNames) ? roleNames : [roleNames];

    const orderClause = [['firstName', 'ASC']];
    const users = await this.findAll({
      where,
      order: orderClause,
      paranoid,
      attributes,
      paginate: false,
      include: [
        {
          association: 'role',
          where: {
            name: {
              [this.Sequelize.Op.in]: roleNamesArray,
            },
          },
        },
      ],
    });

    return users;
  }

  async findUsersByIds(userIds = [], options = {}) {
    return this.findAll({
      where: {
        id: {
          [this.Sequelize.Op.in]: userIds,
        },
      },
      ...options,
    });
  }

  async getPurchasingHead() {
    const purchasingHead = await this.findOne({
      where: {
        roleId: this.userConstants.USER_TYPE_IDS.PURCHASING_HEAD,
      },
    });

    return purchasingHead;
  }
}

module.exports = UserRepository;
