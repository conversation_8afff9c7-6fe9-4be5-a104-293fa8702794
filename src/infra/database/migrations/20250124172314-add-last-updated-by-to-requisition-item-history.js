'use strict';
const { withTimescaleDBCompression } = require('../utils/timescale-db-migration-helper');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await withTimescaleDBCompression(queryInterface, transaction, 'requisition_item_histories', async () => {
        await queryInterface.addColumn('requisition_item_histories', 'last_updated_by', {
          type: Sequelize.INTEGER,
          allowNull: true,
          references: {
            model: 'users',
            key: 'id',
          },
          onUpdate: 'CASCADE',
          onDelete: 'SET NULL',
        }, { transaction });
      });

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      await withTimescaleDBCompression(queryInterface, transaction, 'requisition_item_histories', async () => {
        await queryInterface.removeColumn(
          'requisition_item_histories',
          'last_updated_by',
          { transaction }
        );
      });

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }
};
