class RequisitionItemHistoryService {
  constructor({ db, clientErrors, fastify }) {
    this.db = db;
    this.clientErrors = clientErrors;
    this.fastify = fastify;
  }

  async createRequisitionItemHistoryRecord(requisitionItem, options) {
    const { transaction, userId } = options;
    const isOfm = requisitionItem.itemType === 'ofm' || requisitionItem.itemType === 'ofm-tom';

    try {
      const item = await this.db.sequelize
        .model(isOfm ? 'items' : 'non_ofm_items')
        .findOne({
          attributes: [isOfm ? 'itmDes' : 'itemName'],
          where: { id: requisitionItem.itemId },
          transaction,
        });

      await this.db.sequelize.model('requisition_item_histories').create(
        {
          requisitionId: requisitionItem.requisitionId,
          item: item?.itmDes || item?.itemName || `Unknown Item (ID: ${requisitionItem.itemId})`,
          quantityRequested: requisitionItem.quantity,
          quantityOrdered: 0,
          quantityDelivered: 0,
          lastUpdatedBy: userId || null,
        },
        { transaction },
      );
    } catch (error) {
      this.fastify.log.error(
        'ERROR - requisitionItemHistoryService - createRequisitionItemHistoryRecord: ',
        error.stack,
      );
      throw error;
    }
  }
}

module.exports = RequisitionItemHistoryService;